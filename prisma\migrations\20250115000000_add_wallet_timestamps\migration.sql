-- Migration: Add timestamps to UserWallet for automatic cleanup
-- This migration adds createdAt and updatedAt fields to track wallet age

-- Add createdAt and updatedAt columns to UserWallet table
ALTER TABLE "UserWallet" ADD COLUMN "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "UserWallet" ADD COLUMN "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Create index on createdAt for efficient cleanup queries
CREATE INDEX "UserWallet_createdAt_idx" ON "UserWallet"("createdAt");

-- Create index on status for efficient filtering
CREATE INDEX "UserWallet_status_idx" ON "UserWallet"("status");

-- Update existing records to have current timestamp
UPDATE "UserWallet" SET "createdAt" = CURRENT_TIMESTAMP, "updatedAt" = CURRENT_TIMESTAMP WHERE "createdAt" IS NULL;
