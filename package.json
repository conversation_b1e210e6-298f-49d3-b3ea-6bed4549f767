{"name": "handi-cat_wallet-tracker", "version": "1.0.0", "description": "", "main": "dist/src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "tsc && node ./dist/src/main.js", "start:test": "tsc && node ./dist/src/test.js", "postinstall": "prisma generate", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev --skip-seed", "db:backup": "npx ts-node scripts/backup-db.ts", "db:seed": "npx ts-node scripts/seed-db.ts", "wallets:cleanup": "npx ts-node scripts/cleanup-wallets.ts", "send:alert": "npx ts-node scripts/send-user-alerts.ts", "format": "prettier --write \"**/*.{ts,tsx,md}\"  --ignore-path .gitignore", "format:check": "pnpm format --check", "format:write": "pnpm format --write"}, "keywords": [], "author": "draco", "packageManager": "pnpm@9.15.2", "license": "ISC", "dependencies": {"@metaplex-foundation/mpl-token-metadata": "2.0.0", "@prisma/client": "^5.16.2", "@raydium-io/raydium-sdk": "1.3.1-beta.58", "@solana/spl-token": "^0.3.0", "@solana/spl-token-registry": "^0.2.4574", "@solana/web3.js": "^1.94.0", "axios": "^1.7.7", "bs58": "^6.0.0", "chalk": "4.1.2", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "express": "^4.19.2", "gradient-string": "^2.0.0", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.66.0", "p-limit": "3.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/gradient-string": "^1.1.6", "@types/node": "^20.14.9", "@types/node-cron": "^3.0.11", "@types/node-telegram-bot-api": "^0.64.7", "prettier": "^3.3.3", "prisma": "^5.16.2", "ts-node": "^10.9.2", "typescript": "^5.5.2"}}