import { PublicKey } from '@solana/web3.js'
import { PrismaWalletRepository } from '../repositories/prisma/wallet'
import { TrackWallets } from '../lib/track-wallets'
import { logger } from './logger-service'
import chalk from 'chalk'

export interface BulkWalletInput {
  address: string
  name?: string
}

export interface BulkWalletResult {
  address: string
  name: string
  success: boolean
  error?: string
  action: 'added' | 'removed' | 'skipped'
}

export interface BulkOperationSummary {
  totalProcessed: number
  successful: number
  failed: number
  skipped: number
  results: BulkWalletResult[]
  duration: number
}

/**
 * Bulk Wallet Management Service
 * 
 * Handles adding and removing multiple wallets at once with proper
 * validation, error handling, and progress tracking.
 */
export class BulkWalletService {
  private prismaWalletRepository: PrismaWalletRepository
  private trackWallets: TrackWallets

  constructor() {
    this.prismaWalletRepository = new PrismaWalletRepository()
    this.trackWallets = new TrackWallets()
  }

  /**
   * Add multiple wallets at once
   */
  public async addWalletsBulk(
    userId: string,
    wallets: BulkWalletInput[],
    progressCallback?: (progress: number, current: string) => void
  ): Promise<BulkOperationSummary> {
    const startTime = Date.now()
    const results: BulkWalletResult[] = []

    logger.info('Starting bulk wallet addition', {
      component: 'bulk-wallet-service',
      metadata: {
        userId,
        walletCount: wallets.length
      }
    })

    for (let i = 0; i < wallets.length; i++) {
      const wallet = wallets[i]
      const progress = Math.round(((i + 1) / wallets.length) * 100)
      
      // Call progress callback if provided
      if (progressCallback) {
        progressCallback(progress, wallet.address)
      }

      try {
        // Validate wallet address
        if (!this.isValidSolanaAddress(wallet.address)) {
          results.push({
            address: wallet.address,
            name: wallet.name || 'Unnamed',
            success: false,
            error: 'Invalid Solana address format',
            action: 'skipped'
          })
          continue
        }

        // Check if wallet already exists for this user
        const existingWallet = await this.prismaWalletRepository.getUserWalletByAddress(userId, wallet.address)
        if (existingWallet) {
          results.push({
            address: wallet.address,
            name: wallet.name || existingWallet.name,
            success: false,
            error: 'Wallet already being tracked',
            action: 'skipped'
          })
          continue
        }

        // Add wallet
        const walletName = wallet.name || `Wallet ${i + 1}`
        const addedWallet = await this.prismaWalletRepository.createUserWallet(
          userId,
          wallet.address,
          walletName
        )

        if (addedWallet) {
          results.push({
            address: wallet.address,
            name: walletName,
            success: true,
            action: 'added'
          })

          logger.logWalletEvent('bulk_added', wallet.address, {
            walletName,
            userId,
            bulkOperation: true
          })
        } else {
          results.push({
            address: wallet.address,
            name: walletName,
            success: false,
            error: 'Failed to create wallet in database',
            action: 'skipped'
          })
        }

      } catch (error) {
        logger.error('Error adding wallet in bulk operation', error as Error, {
          component: 'bulk-wallet-service',
          walletAddress: wallet.address,
          metadata: { userId, bulkOperation: true }
        })

        results.push({
          address: wallet.address,
          name: wallet.name || 'Unnamed',
          success: false,
          error: (error as Error).message,
          action: 'skipped'
        })
      }

      // Small delay to prevent overwhelming the system
      if (i < wallets.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    // Start tracking all successfully added wallets
    try {
      await this.trackWallets.setupWalletWatcher({ event: 'bulk_add' })
    } catch (error) {
      logger.error('Error setting up wallet watchers after bulk add', error as Error, {
        component: 'bulk-wallet-service',
        metadata: { userId }
      })
    }

    const duration = Date.now() - startTime
    const summary: BulkOperationSummary = {
      totalProcessed: wallets.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success && r.action !== 'skipped').length,
      skipped: results.filter(r => r.action === 'skipped').length,
      results,
      duration
    }

    logger.info('Bulk wallet addition completed', {
      component: 'bulk-wallet-service',
      metadata: {
        userId,
        ...summary
      }
    })

    return summary
  }

  /**
   * Remove multiple wallets at once
   */
  public async removeWalletsBulk(
    userId: string,
    walletAddresses: string[],
    progressCallback?: (progress: number, current: string) => void
  ): Promise<BulkOperationSummary> {
    const startTime = Date.now()
    const results: BulkWalletResult[] = []

    logger.info('Starting bulk wallet removal', {
      component: 'bulk-wallet-service',
      metadata: {
        userId,
        walletCount: walletAddresses.length
      }
    })

    for (let i = 0; i < walletAddresses.length; i++) {
      const address = walletAddresses[i]
      const progress = Math.round(((i + 1) / walletAddresses.length) * 100)
      
      // Call progress callback if provided
      if (progressCallback) {
        progressCallback(progress, address)
      }

      try {
        // Check if wallet exists for this user
        const existingWallet = await this.prismaWalletRepository.getUserWalletByAddress(userId, address)
        if (!existingWallet) {
          results.push({
            address,
            name: 'Unknown',
            success: false,
            error: 'Wallet not found in your tracking list',
            action: 'skipped'
          })
          continue
        }

        // Remove wallet
        const removed = await this.prismaWalletRepository.deleteUserWallet(userId, existingWallet.walletId)
        
        if (removed) {
          // Stop tracking if no other users are tracking this wallet
          await this.trackWallets.stopWatchingWallet(existingWallet.walletId)

          results.push({
            address,
            name: existingWallet.name,
            success: true,
            action: 'removed'
          })

          logger.logWalletEvent('bulk_removed', address, {
            walletName: existingWallet.name,
            userId,
            bulkOperation: true
          })
        } else {
          results.push({
            address,
            name: existingWallet.name,
            success: false,
            error: 'Failed to remove wallet from database',
            action: 'skipped'
          })
        }

      } catch (error) {
        logger.error('Error removing wallet in bulk operation', error as Error, {
          component: 'bulk-wallet-service',
          walletAddress: address,
          metadata: { userId, bulkOperation: true }
        })

        results.push({
          address,
          name: 'Unknown',
          success: false,
          error: (error as Error).message,
          action: 'skipped'
        })
      }

      // Small delay to prevent overwhelming the system
      if (i < walletAddresses.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    const duration = Date.now() - startTime
    const summary: BulkOperationSummary = {
      totalProcessed: walletAddresses.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success && r.action !== 'skipped').length,
      skipped: results.filter(r => r.action === 'skipped').length,
      results,
      duration
    }

    logger.info('Bulk wallet removal completed', {
      component: 'bulk-wallet-service',
      metadata: {
        userId,
        ...summary
      }
    })

    return summary
  }

  /**
   * Parse wallet input from text (supports multiple formats)
   */
  public parseWalletInput(text: string): BulkWalletInput[] {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0)
    const wallets: BulkWalletInput[] = []

    for (const line of lines) {
      // Support different formats:
      // 1. "address name" (space separated)
      // 2. "address,name" (comma separated)
      // 3. "address" (address only)
      
      let address = ''
      let name = ''

      if (line.includes(',')) {
        // Comma separated format
        const parts = line.split(',').map(p => p.trim())
        address = parts[0]
        name = parts[1] || ''
      } else if (line.includes(' ')) {
        // Space separated format
        const parts = line.split(' ')
        address = parts[0]
        name = parts.slice(1).join(' ').trim()
      } else {
        // Address only
        address = line
        name = ''
      }

      if (address) {
        wallets.push({ address, name })
      }
    }

    return wallets
  }

  /**
   * Validate Solana address format
   */
  private isValidSolanaAddress(address: string): boolean {
    try {
      new PublicKey(address)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get user's wallet count for validation
   */
  public async getUserWalletCount(userId: string): Promise<number> {
    try {
      const wallets = await this.prismaWalletRepository.getUserWallets(userId)
      return wallets.length
    } catch (error) {
      logger.error('Error getting user wallet count', error as Error, {
        component: 'bulk-wallet-service',
        metadata: { userId }
      })
      return 0
    }
  }

  /**
   * Generate summary message for bulk operations
   */
  public generateSummaryMessage(summary: BulkOperationSummary, operation: 'add' | 'remove'): string {
    const { totalProcessed, successful, failed, skipped, duration } = summary
    const action = operation === 'add' ? 'Added' : 'Removed'
    const actionLower = operation === 'add' ? 'added' : 'removed'

    let message = `
📊 <b>Bulk ${action} Summary</b>

✅ <b>Successfully ${actionLower}:</b> ${successful}
❌ <b>Failed:</b> ${failed}
⏭️ <b>Skipped:</b> ${skipped}
📝 <b>Total processed:</b> ${totalProcessed}
⏱️ <b>Duration:</b> ${(duration / 1000).toFixed(1)}s

`

    // Show first few results
    const maxResults = 10
    const resultsToShow = summary.results.slice(0, maxResults)

    if (resultsToShow.length > 0) {
      message += `<b>Results:</b>\n`
      for (const result of resultsToShow) {
        const icon = result.success ? '✅' : result.action === 'skipped' ? '⏭️' : '❌'
        const shortAddress = `${result.address.substring(0, 8)}...${result.address.substring(result.address.length - 4)}`
        message += `${icon} ${shortAddress} ${result.name}\n`
        if (result.error) {
          message += `   <i>${result.error}</i>\n`
        }
      }

      if (summary.results.length > maxResults) {
        message += `\n<i>... and ${summary.results.length - maxResults} more</i>\n`
      }
    }

    return message
  }
}
