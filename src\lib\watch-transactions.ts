import { <PERSON>, <PERSON><PERSON><PERSON>, Lo<PERSON><PERSON>ilter, Logs } from '@solana/web3.js'
import { ValidTransactions } from './valid-transactions'
import EventEmitter from 'events'
import { TransactionParser } from '../parsers/transaction-parser'
import { SendTransactionMsgHandler } from '../bot/handlers/send-tx-msg-handler'
import { bot } from '../providers/telegram'
import { SwapType, WalletWithUsers } from '../types/swap-types'
import { RateLimit } from './rate-limit'
import chalk from 'chalk'
import { RpcConnectionManager } from '../providers/solana'
import pLimit from 'p-limit'
import { CronJobs } from './cron-jobs'
import { PrismaUserRepository } from '../repositories/prisma/user'
import { WalletPool } from '../config/wallet-pool'
import { logger } from '../services/logger-service'
import TelegramBot from 'node-telegram-bot-api'

export class WatchTransaction extends EventEmitter {
  private walletTransactions: Map<string, { count: number; startTime: number }>
  private walletConnections: Map<string, Connection> // Track which connection each wallet uses
  private rateLimit: RateLimit
  private prismaUserRepository: PrismaUserRepository

  constructor() {
    super()

    this.walletTransactions = new Map()
    this.walletConnections = new Map()

    // this.trackedWallets = new Set()

    this.rateLimit = new RateLimit(WalletPool.subscriptions)

    this.prismaUserRepository = new PrismaUserRepository()
  }

  public async watchSocket(wallets: WalletWithUsers[]): Promise<void> {
    try {
      for (const wallet of wallets) {
        const publicKey = new PublicKey(wallet.address)
        const walletAddress = publicKey.toBase58()

        // Check if a subscription already exists for this wallet address
        if (WalletPool.subscriptions.has(walletAddress)) {
          // console.log(`Already watching for: ${walletAddress}`)
          continue // Skip re-subscribing
        }

        console.log(chalk.greenBright(`Watching transactions for wallet: `) + chalk.yellowBright.bold(walletAddress))

        // Log wallet tracking start
        logger.logWalletEvent('tracking_started', walletAddress, {
          walletName: wallet.userWallets[0]?.name,
          userCount: wallet.userWallets.length
        })

        // Initialize transaction count and timestamp
        this.walletTransactions.set(walletAddress, { count: 0, startTime: Date.now() })

        // Get a dedicated Helius connection for this wallet (load balancing)
        const heliusConnection = RpcConnectionManager.getNextHeliusConnection()
        this.walletConnections.set(walletAddress, heliusConnection)

        console.log(chalk.blueBright(`Using Helius connection for wallet: ${walletAddress}`))

        // Start real-time log with error handling for API key failures
        const subscriptionId = await this.createSubscriptionWithRetry(heliusConnection, publicKey, walletAddress, wallet)

        if (!subscriptionId) {
          console.error(`Failed to create subscription for wallet: ${walletAddress}`)
          continue
        }

        // Store subscription ID
        WalletPool.subscriptions.set(wallet.address, subscriptionId)
        console.log(
          chalk.greenBright(`Subscribed to logs with subscription ID: `) + chalk.yellowBright.bold(subscriptionId),
        )
      }
    } catch (error) {
      console.error('Error in watchSocket:', error)
    }
  }

  /**
   * Create subscription with retry logic and failover to different Helius keys
   */
  private async createSubscriptionWithRetry(
    connection: Connection,
    publicKey: PublicKey,
    walletAddress: string,
    wallet: WalletWithUsers,
    maxRetries = 3
  ): Promise<number | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const subscriptionId = connection.onLogs(
          publicKey,
          async (logs, ctx) => {
            // Exclude wallets that have reached the limit
            if (WalletPool.bannedWallets.has(walletAddress)) {
              console.log(`Wallet ${walletAddress} is excluded from logging.`)
              return
            }

            const { isRelevant, swap } = ValidTransactions.isRelevantTransaction(logs)

            if (!isRelevant) {
              return
            }

            // check txs per second
            const walletData = this.walletTransactions.get(walletAddress)
            if (!walletData) {
              return
            }

            const isWalletRateLimited = await this.rateLimit.txPerSecondCap({
              wallet,
              bot,
              excludedWallets: WalletPool.bannedWallets,
              walletData,
            })

            if (isWalletRateLimited) {
              return
            }

            const transactionSignature = logs.signature

            const transactionDetails = await this.getParsedTransaction(transactionSignature)

            if (!transactionDetails || transactionDetails[0] === null) {
              return
            }

            // Parse transaction
            const solPriceUsd = CronJobs.getSolPrice()
            const transactionParser = new TransactionParser(transactionSignature)

            if (
              swap === 'raydium' ||
              swap === 'jupiter' ||
              swap === 'pumpfun' ||
              swap === 'mint_pumpfun' ||
              swap === 'pumpfun_amm'
            ) {
              const parsed = await transactionParser.parseDefiTransaction(
                transactionDetails,
                swap,
                solPriceUsd,
                walletAddress,
              )
              if (!parsed) {
                return
              }
              console.log(parsed.description)

              await this.sendMessageToUsers(wallet, parsed, (handler, parsedData, userId) =>
                handler.sendTransactionMessage(parsedData, userId),
              )
            } else if (swap === 'sol_transfer') {
              const parsed = await transactionParser.parseSolTransfer(transactionDetails, solPriceUsd, walletAddress)
              if (!parsed) {
                return
              }
              console.log(parsed.description)

              await this.sendMessageToUsers(wallet, parsed, (handler, parsedData, userId) =>
                handler.sendTransferMessage(parsedData, userId),
              )
            }
          },
          'processed',
        )

        console.log(chalk.greenBright(`Successfully created subscription ${subscriptionId} for wallet: ${walletAddress}`))

        logger.logWalletEvent('subscription_created', walletAddress, {
          subscriptionId,
          attempt,
          connectionType: 'helius'
        })

        return subscriptionId

      } catch (error) {
        console.error(`Attempt ${attempt}: Failed to create subscription for wallet ${walletAddress}:`, error)

        logger.error(`Failed to create subscription for wallet ${walletAddress}`, error as Error, {
          component: 'wallet-subscription',
          walletAddress,
          metadata: { attempt, maxRetries }
        })

        if (attempt === maxRetries) {
          // Mark this Helius key as failed and try a different one
          RpcConnectionManager.markHeliusKeyAsFailed(connection)

          // Try with a different Helius connection
          const newConnection = RpcConnectionManager.getNextHeliusConnection()
          if (newConnection !== connection) {
            console.log(`Retrying with different Helius connection for wallet: ${walletAddress}`)
            this.walletConnections.set(walletAddress, newConnection)
            return this.createSubscriptionWithRetry(newConnection, publicKey, walletAddress, wallet, 2)
          }
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }

    console.error(`Failed to create subscription for wallet ${walletAddress} after ${maxRetries} attempts`)
    return null
  }

  public async getParsedTransaction(transactionSignature: string, retries = 4) {
    const timer = logger.createTimer('getParsedTransaction', 'transaction-fetcher')

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        // Use random connection for load balancing
        const connection = RpcConnectionManager.getRandomConnection()

        const startTime = Date.now()
        const transactionDetails = await connection.getParsedTransactions(
          [transactionSignature],
          {
            maxSupportedTransactionVersion: 0,
          },
        )
        const duration = Date.now() - startTime

        // Log RPC call
        logger.logRpcCall(
          connection.rpcEndpoint,
          'getParsedTransactions',
          !!transactionDetails?.[0],
          duration
        )

        if (transactionDetails && transactionDetails[0] !== null) {
          logger.logTransactionEvent('fetched_successfully', transactionSignature, undefined, {
            attempt,
            duration
          })
          timer()
          return transactionDetails
        }

        logger.debug(`No transaction details found for ${transactionSignature}`, {
          component: 'transaction-fetcher',
          transactionSignature,
          metadata: { attempt, retries }
        })

        console.log(`Attempt ${attempt}: No transaction details found for ${transactionSignature}`)
      } catch (error) {
        console.error(`Attempt ${attempt}: Error fetching transaction details`, error)

        logger.error(`Error fetching transaction details`, error as Error, {
          component: 'transaction-fetcher',
          transactionSignature,
          metadata: { attempt, retries }
        })

        // If we're on the last attempt, try with a Helius connection as fallback
        if (attempt === retries) {
          try {
            console.log(`Fallback: Trying with Helius connection for ${transactionSignature}`)
            const heliusConnection = RpcConnectionManager.getRandomHeliusConnection()
            const transactionDetails = await heliusConnection.getParsedTransactions(
              [transactionSignature],
              {
                maxSupportedTransactionVersion: 0,
              },
            )

            if (transactionDetails && transactionDetails[0] !== null) {
              return transactionDetails
            }
          } catch (heliusError) {
            console.error(`Helius fallback also failed for ${transactionSignature}:`, heliusError)
          }
        }
      }

      // Delay before retrying
      await new Promise((resolve) => setTimeout(resolve, 1000 * attempt))
    }

    console.error(`Failed to fetch transaction details after ${retries} retries for signature:`, transactionSignature)
    return null
  }

  /**
   * Get the connection used for a specific wallet
   */
  public getWalletConnection(walletAddress: string): Connection | undefined {
    return this.walletConnections.get(walletAddress)
  }

  /**
   * Remove wallet connection tracking
   */
  public removeWalletConnection(walletAddress: string): void {
    this.walletConnections.delete(walletAddress)
    this.walletTransactions.delete(walletAddress)
  }

  private async sendMessageToUsers<T>(
    wallet: WalletWithUsers,
    parsed: T,
    sendMessageFn: (
      handler: SendTransactionMsgHandler,
      parsed: T,
      userId: string,
    ) => Promise<TelegramBot.Message | undefined>,
  ) {
    const sendMessageHandler = new SendTransactionMsgHandler(bot)

    const pausedUsers = (await this.prismaUserRepository.getPausedUsers(wallet.userWallets.map((w) => w.userId))) || []

    const activeUsers = wallet.userWallets.filter((w) => !pausedUsers || !pausedUsers.includes(w.userId))

    // Remove duplicate users
    const uniqueActiveUsers = Array.from(new Set(activeUsers.map((user) => user.userId))).map((userId) =>
      activeUsers.find((user) => user.userId === userId),
    )

    const limit = pLimit(20)

    const tasks = uniqueActiveUsers.map((user) =>
      limit(async () => {
        if (user) {
          try {
            await sendMessageFn(sendMessageHandler, parsed, user.userId)
          } catch (error) {
            console.log(`Error sending message to user ${user.userId}`)
          }
        }
      }),
    )

    await Promise.all(tasks)
  }
}
