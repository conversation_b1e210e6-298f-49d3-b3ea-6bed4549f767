{"code": "CRITICAL_ERROR", "component": "process", "context": {"component": "process", "metadata": {"promise": "[object Promise]"}}, "level": "error", "message": "Error tracked Unhandled Promise Rejection", "severity": "critical", "stack": "TypeError: allWallets is not iterable (cannot read property undefined)\n    at TrackWallets.<anonymous> (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\dist\\src\\lib\\track-wallets.js:150:97)\n    at Generator.next (<anonymous>)\n    at fulfilled (C:\\Users\\<USER>\\Desktop\\handi-cat_wallet-tracker-main\\dist\\src\\lib\\track-wallets.js:5:58)", "timestamp": "2025-05-29 13:05:47"}