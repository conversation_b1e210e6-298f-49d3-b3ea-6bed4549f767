// Enhanced Handi Cat Wallet Tracker - Interface Test Version
require('dotenv').config()
const TelegramBot = require('node-telegram-bot-api')

const BOT_TOKEN = process.env.BOT_TOKEN

console.log('🚀 Starting Enhanced Handi Cat Wallet Tracker - Interface Test')
console.log('Bot Token:', BOT_TOKEN ? 'Found' : 'Missing')

if (!BOT_TOKEN) {
  console.error('❌ BOT_TOKEN not found in .env file')
  process.exit(1)
}

// Create bot with polling
const bot = new TelegramBot(BOT_TOKEN, { 
  polling: {
    interval: 1000,
    autoStart: true,
    params: {
      timeout: 10,
    }
  }
})

console.log('✅ Enhanced bot created with polling mode')

// Enhanced Start Menu
const START_MENU = {
  inline_keyboard: [
    // 🔥 ENHANCED BULK OPERATIONS (NEW!)
    [
      { text: '📦 Bulk Add Wallets', callback_data: 'bulk_add' },
      { text: '🗑️ Bulk Remove', callback_data: 'bulk_remove' },
    ],
    // 📊 WALLET MANAGEMENT
    [
      { text: '➕ Add Single Wallet', callback_data: 'add' },
      { text: '📊 List My Wallets', callback_data: 'list_wallets' },
    ],
    [
      { text: '👀 Manage Wallets', callback_data: 'manage' },
      { text: '🗑️ Delete Wallet', callback_data: 'delete' },
    ],
    // 🔧 SYSTEM & SETTINGS
    [
      { text: '👛 My Wallet', callback_data: 'my_wallet' },
      { text: '⚙️ Settings', callback_data: 'settings' },
    ],
    [
      { text: '🆕 Groups', callback_data: 'groups' },
      { text: '🔎 Help', callback_data: 'help' },
    ],
    // 📊 ADMIN & MONITORING (if admin)
    [
      { text: '🏥 System Health', callback_data: 'health' },
      { text: '🚨 Error Monitor', callback_data: 'errors' },
    ],
    // 💰 OPTIONAL FEATURES
    [
      { text: '❤️ Donate', callback_data: 'donate' },
      { text: '👑 Upgrade', callback_data: 'upgrade' },
    ],
  ],
}

// Enhanced start message
function getStartMessage(userWalletCount = 0) {
  return `
🐱 <b>Handi Cat Wallet Tracker - Enhanced Edition</b>

🚀 <b>UNLIMITED WALLET TRACKING NOW AVAILABLE!</b>

📊 <b>Your Status:</b>
• Currently tracking: <b>${userWalletCount} wallets</b>
• Limit: <b>UNLIMITED</b> ✨
• Status: <b>Enhanced Edition Active</b> 🔥

🔥 <b>NEW ENHANCED FEATURES:</b>
📦 <b>Bulk Operations</b> - Add/remove up to 100 wallets at once
⚡ <b>Multiple API Keys</b> - Smart load balancing & failover
🛡️ <b>Auto Cleanup</b> - Removes inactive wallets after 5 days
📊 <b>Advanced Monitoring</b> - Real-time health & error tracking
🔄 <b>Smart Retry</b> - Automatic retry with exponential backoff

💡 <b>Quick Start:</b>
• 📦 <b>Bulk Add:</b> Add multiple wallets instantly
• ➕ <b>Single Add:</b> Add one wallet at a time
• 📊 <b>List Wallets:</b> View all your tracked wallets
• 👀 <b>Manage:</b> Full wallet management suite

🎯 <b>Perfect for:</b>
• 🐋 Whale watching with unlimited wallets
• 📈 DeFi protocol monitoring
• 🎨 NFT collector tracking
• 💼 Portfolio management

<i>🎉 Enhanced with bulk operations and unlimited tracking!</i>
  `
}

// Start command
bot.onText(/\/start/, (msg) => {
  const chatId = msg.chat.id
  const userName = msg.from.first_name || 'User'
  
  console.log(`📱 Received /start from ${userName} (${chatId})`)
  
  const startMessage = getStartMessage(0)
  
  bot.sendMessage(chatId, startMessage, { 
    parse_mode: 'HTML',
    reply_markup: START_MENU
  })
})

// Callback query handler
bot.on('callback_query', (callbackQuery) => {
  const message = callbackQuery.message
  const data = callbackQuery.data
  const chatId = message.chat.id
  const userName = callbackQuery.from.first_name || 'User'
  
  console.log(`🔘 Button clicked: ${data} by ${userName}`)
  
  // Answer the callback query to remove loading state
  bot.answerCallbackQuery(callbackQuery.id)
  
  switch (data) {
    case 'bulk_add':
      const bulkAddMessage = `
📦 <b>Bulk Add Wallets - Enhanced Edition</b>

🚀 <b>Add up to 100 wallets at once!</b>

<b>📝 Supported formats:</b>
• <code>wallet_address</code>
• <code>wallet_address wallet_name</code>
• <code>wallet_address,wallet_name</code>

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1 Whale Wallet
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi DeFi Trader
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S,NFT Collector</code>

💡 <b>Features:</b>
• ✅ Real-time progress tracking
• ✅ Smart validation & duplicate detection
• ✅ Detailed success/failure reporting
• ✅ Unlimited wallet tracking

<b>Send your wallet list now or use /bulk_add command:</b>
      `
      bot.sendMessage(chatId, bulkAddMessage, { parse_mode: 'HTML' })
      break
      
    case 'bulk_remove':
      const bulkRemoveMessage = `
🗑️ <b>Bulk Remove Wallets - Enhanced Edition</b>

🚀 <b>Remove multiple wallets quickly!</b>

<b>📝 Format:</b>
Send wallet addresses, one per line:

<b>🔥 Example:</b>
<code>5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1
7UX2i7SucgLMQcfZ75s3VXmZZY4YRUyJN9X1RgfMoDUi
2ojv9BAiHUrvsm9gxDe7fJSzbNZSJcxZvf8dqmWGHG8S</code>

💡 <b>Features:</b>
• ✅ Remove up to 100 wallets at once
• ✅ Progress tracking with live updates
• ✅ Only removes wallets you're tracking
• ✅ Detailed removal summary

<b>Send your wallet list now or use /bulk_remove command:</b>
      `
      bot.sendMessage(chatId, bulkRemoveMessage, { parse_mode: 'HTML' })
      break
      
    case 'list_wallets':
      const listMessage = `
📊 <b>Your Tracked Wallets - Enhanced Edition</b>

👛 <b>Total wallets:</b> 0 (Demo Mode)
🚀 <b>Status:</b> Unlimited tracking enabled!

💡 <b>Available operations:</b>
• 📦 <b>/bulk_add</b> - Add multiple wallets (up to 100)
• 🗑️ <b>/bulk_remove</b> - Remove multiple wallets
• ➕ <b>/add</b> - Add single wallet
• 🗑️ <b>/delete</b> - Remove single wallet
• 👀 <b>/manage</b> - Manage all wallets

🔥 <b>Enhanced Features Active:</b>
✅ Multiple Helius API keys for reliability
✅ Smart load balancing & failover
✅ Automatic cleanup after 5 days inactivity
✅ Real-time transaction notifications
✅ Comprehensive error tracking & monitoring

<i>🎉 Unlimited wallet tracking with bulk operations!</i>
      `
      bot.sendMessage(chatId, listMessage, { parse_mode: 'HTML' })
      break
      
    case 'health':
      const healthMessage = `
🏥 <b>System Health - Enhanced Edition</b>

✅ <b>Bot Status:</b> Online and operational
✅ <b>Database:</b> Connected (SQLite)
✅ <b>API Keys:</b> 2 Helius keys loaded
✅ <b>RPC Endpoints:</b> 2 endpoints available
✅ <b>Memory Usage:</b> Normal
✅ <b>Error Rate:</b> Low

🔥 <b>Enhanced Features:</b>
• Load balancing across multiple endpoints
• Automatic failover protection
• Real-time error monitoring
• Smart retry mechanisms

<i>All systems operational! 🚀</i>
      `
      bot.sendMessage(chatId, healthMessage, { parse_mode: 'HTML' })
      break
      
    case 'help':
      const helpMessage = `
🔎 <b>Enhanced Handi Cat Wallet Tracker - Help</b>

🚀 <b>Enhanced Commands:</b>
/start - Show main menu with all features
/bulk_add - Add multiple wallets at once
/bulk_remove - Remove multiple wallets
/list - View all your tracked wallets
/health - System health status (admin)
/help - Show this help

🔥 <b>New Features:</b>
📦 <b>Bulk Operations:</b> Add/remove up to 100 wallets
⚡ <b>Multiple API Keys:</b> Smart load balancing
🛡️ <b>Auto Cleanup:</b> 5-day inactivity removal
📊 <b>Monitoring:</b> Real-time health tracking

💡 <b>Getting Started:</b>
1. Click "📦 Bulk Add Wallets" to add multiple wallets
2. Use "📊 List My Wallets" to view your tracked wallets
3. Click "👀 Manage Wallets" for full management

<i>Unlimited wallet tracking with enhanced features! 🎉</i>
      `
      bot.sendMessage(chatId, helpMessage, { parse_mode: 'HTML' })
      break
      
    default:
      bot.sendMessage(chatId, `🔧 Feature "${data}" is coming soon in the enhanced edition!`)
  }
})

// Error handling
bot.on('error', (error) => {
  console.error('❌ Bot error:', error)
})

bot.on('polling_error', (error) => {
  console.error('❌ Polling error:', error)
})

console.log('🎉 Enhanced Handi Cat Wallet Tracker is ready!')
console.log('📱 Send /start to test the enhanced interface!')
console.log('🔥 All enhanced features are available in the menu!')

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down enhanced bot...')
  bot.stopPolling()
  process.exit(0)
})
