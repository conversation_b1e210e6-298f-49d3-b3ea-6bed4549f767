import { Connection, clusterApiUrl } from '@solana/web3.js'
import chalk from 'chalk'
import dotenv from 'dotenv'
import { logger } from '../services/logger-service'

dotenv.config()

// Multiple Helius API keys for load balancing and unlimited wallet tracking
const HELIUS_API_KEYS =
  process.env.HELIUS_API_KEYS?.split(',')
    .map((key) => key.trim())
    .filter(Boolean) ?? []

// Fallback to single key for backward compatibility
if (HELIUS_API_KEYS.length === 0 && process.env.HELIUS_API_KEY) {
  HELIUS_API_KEYS.push(process.env.HELIUS_API_KEY)
}

const RPC_ENDPOINTS =
  process.env.RPC_ENDPOINTS?.split(',')
    .map((url) => url.trim())
    .filter(Boolean) ?? []

console.log(chalk.bold.greenBright(`LOADED ${RPC_ENDPOINTS.length} RPC ENDPOINTS`))
console.log(chalk.bold.blueBright(`LOADED ${HELIUS_API_KEYS.length} HELIUS API KEYS`))

// Log configuration details
logger.info('Solana provider initialized', {
  component: 'solana-provider',
  metadata: {
    rpcEndpointsCount: RPC_ENDPOINTS.length,
    heliusKeysCount: HELIUS_API_KEYS.length,
    rpcEndpoints: RPC_ENDPOINTS.map(url => url.split('?')[0]), // Hide API keys in logs
  }
})

// Enhanced RPC Connection Manager with multiple Helius API key support
export class RpcConnectionManager {
  static connections: Connection[] = RPC_ENDPOINTS.map((url) => new Connection(url, 'confirmed'))

  // Multiple Helius connections for load balancing
  static heliusConnections: Connection[] = HELIUS_API_KEYS.map(
    (key) => new Connection(`https://mainnet.helius-rpc.com/?api-key=${key}`, 'processed')
  )

  // Current index for round-robin rotation
  private static currentHeliusIndex = 0
  private static failedKeys = new Set<number>()

  // Backward compatibility - use first Helius connection as default
  static logConnection = RpcConnectionManager.heliusConnections[0] || new Connection('https://api.mainnet-beta.solana.com', 'processed')

  static getRandomConnection(): Connection {
    if (RpcConnectionManager.connections.length === 0) {
      logger.warn('No RPC endpoints configured, using default Solana RPC', {
        component: 'rpc-connection-manager'
      })
      console.warn('No RPC endpoints configured, using default Solana RPC')
      return new Connection('https://api.mainnet-beta.solana.com', 'confirmed')
    }
    const randomIndex = Math.floor(Math.random() * RpcConnectionManager.connections.length)

    logger.debug('Selected random RPC connection', {
      component: 'rpc-connection-manager',
      metadata: { connectionIndex: randomIndex, totalConnections: RpcConnectionManager.connections.length }
    })

    return RpcConnectionManager.connections[randomIndex]
  }

  /**
   * Get the next available Helius connection using round-robin with failover
   */
  static getNextHeliusConnection(): Connection {
    if (RpcConnectionManager.heliusConnections.length === 0) {
      console.warn('No Helius API keys configured, falling back to default RPC')
      return new Connection('https://api.mainnet-beta.solana.com', 'processed')
    }

    // If all keys have failed, reset the failed set and try again
    if (RpcConnectionManager.failedKeys.size >= RpcConnectionManager.heliusConnections.length) {
      console.log('All Helius keys failed, resetting failed keys set')
      RpcConnectionManager.failedKeys.clear()
    }

    // Find next available key
    let attempts = 0
    while (attempts < RpcConnectionManager.heliusConnections.length) {
      const connection = RpcConnectionManager.heliusConnections[RpcConnectionManager.currentHeliusIndex]

      // Move to next index for round-robin
      RpcConnectionManager.currentHeliusIndex =
        (RpcConnectionManager.currentHeliusIndex + 1) % RpcConnectionManager.heliusConnections.length

      // Check if this key has failed recently
      if (!RpcConnectionManager.failedKeys.has(RpcConnectionManager.currentHeliusIndex)) {
        return connection
      }

      attempts++
    }

    // If we get here, return the first connection as fallback
    console.warn('All Helius connections marked as failed, using first connection as fallback')
    return RpcConnectionManager.heliusConnections[0]
  }

  /**
   * Mark a Helius API key as failed
   */
  static markHeliusKeyAsFailed(connection: Connection): void {
    const index = RpcConnectionManager.heliusConnections.indexOf(connection)
    if (index !== -1) {
      RpcConnectionManager.failedKeys.add(index)

      logger.warn('Helius API key marked as failed', {
        component: 'helius-connection-manager',
        metadata: {
          keyIndex: index,
          totalKeys: RpcConnectionManager.heliusConnections.length,
          failedKeysCount: RpcConnectionManager.failedKeys.size
        }
      })

      console.warn(`Marked Helius key at index ${index} as failed`)

      // Auto-recover failed keys after 5 minutes
      setTimeout(() => {
        RpcConnectionManager.failedKeys.delete(index)

        logger.info('Helius API key recovered', {
          component: 'helius-connection-manager',
          metadata: {
            keyIndex: index,
            failedKeysCount: RpcConnectionManager.failedKeys.size
          }
        })

        console.log(`Recovered Helius key at index ${index}`)
      }, 5 * 60 * 1000)
    }
  }

  /**
   * Get a random Helius connection for load balancing
   */
  static getRandomHeliusConnection(): Connection {
    if (RpcConnectionManager.heliusConnections.length === 0) {
      console.warn('No Helius API keys configured, falling back to default RPC')
      return new Connection('https://api.mainnet-beta.solana.com', 'processed')
    }

    const availableConnections = RpcConnectionManager.heliusConnections.filter(
      (_, index) => !RpcConnectionManager.failedKeys.has(index)
    )

    if (availableConnections.length === 0) {
      console.warn('No available Helius connections, using first connection as fallback')
      return RpcConnectionManager.heliusConnections[0]
    }

    const randomIndex = Math.floor(Math.random() * availableConnections.length)
    return availableConnections[randomIndex]
  }

  static resetLogConnection() {
    RpcConnectionManager.logConnection = RpcConnectionManager.getNextHeliusConnection()
  }

  /**
   * Get connection statistics
   */
  static getConnectionStats() {
    return {
      totalRpcEndpoints: RpcConnectionManager.connections.length,
      totalHeliusKeys: RpcConnectionManager.heliusConnections.length,
      failedHeliusKeys: RpcConnectionManager.failedKeys.size,
      availableHeliusKeys: RpcConnectionManager.heliusConnections.length - RpcConnectionManager.failedKeys.size
    }
  }
}
