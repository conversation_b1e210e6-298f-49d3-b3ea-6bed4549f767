import winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'
import chalk from 'chalk'
import fs from 'fs'
import path from 'path'
import dotenv from 'dotenv'

dotenv.config()

export interface LogContext {
  component?: string
  walletAddress?: string
  transactionSignature?: string
  apiKey?: string
  error?: Error
  duration?: number
  metadata?: Record<string, any>
}

export interface ErrorDetails {
  message: string
  stack?: string
  code?: string
  statusCode?: number
  component: string
  context?: LogContext
  timestamp: Date
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface PerformanceMetrics {
  operation: string
  duration: number
  success: boolean
  component: string
  metadata?: Record<string, any>
}

/**
 * Advanced Logger Service
 * 
 * Provides comprehensive logging, error tracking, and performance monitoring
 * for the wallet tracker system.
 */
export class LoggerService {
  private static instance: LoggerService
  private logger: winston.Logger
  private errorLogger: winston.Logger
  private performanceLogger: winston.Logger
  private config: {
    logLevel: string
    enableFileLogging: boolean
    logDirectory: string
    maxLogFileSize: number
    maxLogFiles: number
    enableErrorTracking: boolean
    enablePerformanceLogging: boolean
    slowOperationThreshold: number
    enableConsoleAlerts: boolean
    enableFileAlerts: boolean
  }

  private constructor() {
    this.config = {
      logLevel: process.env.LOG_LEVEL || 'info',
      enableFileLogging: process.env.ENABLE_FILE_LOGGING === 'true',
      logDirectory: process.env.LOG_DIRECTORY || 'logs',
      maxLogFileSize: parseInt(process.env.MAX_LOG_FILE_SIZE || '50'),
      maxLogFiles: parseInt(process.env.MAX_LOG_FILES || '10'),
      enableErrorTracking: process.env.ENABLE_ERROR_TRACKING === 'true',
      enablePerformanceLogging: process.env.ENABLE_PERFORMANCE_LOGGING === 'true',
      slowOperationThreshold: parseInt(process.env.SLOW_OPERATION_THRESHOLD || '5000'),
      enableConsoleAlerts: process.env.ENABLE_CONSOLE_ALERTS === 'true',
      enableFileAlerts: process.env.ENABLE_FILE_ALERTS === 'true'
    }

    this.initializeLoggers()
    this.setupErrorHandlers()
  }

  public static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService()
    }
    return LoggerService.instance
  }

  private initializeLoggers(): void {
    // Ensure log directory exists
    if (this.config.enableFileLogging) {
      if (!fs.existsSync(this.config.logDirectory)) {
        fs.mkdirSync(this.config.logDirectory, { recursive: true })
      }
    }

    // Custom format for logs
    const logFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, component, ...meta }) => {
        const componentStr = component ? `[${component}]` : ''
        return `${timestamp} ${level.toUpperCase()} ${componentStr} ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`
      })
    )

    // Console format
    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({ format: 'HH:mm:ss' }),
      winston.format.printf(({ timestamp, level, message, component }) => {
        const componentStr = component ? chalk.gray(`[${component}]`) : ''
        return `${chalk.gray(timestamp)} ${level} ${componentStr} ${message}`
      })
    )

    // Main logger transports
    const transports: winston.transport[] = [
      new winston.transports.Console({
        format: consoleFormat,
        level: this.config.logLevel
      })
    ]

    // Add file transports if enabled
    if (this.config.enableFileLogging) {
      // General log file
      transports.push(
        new DailyRotateFile({
          filename: path.join(this.config.logDirectory, 'wallet-tracker-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: `${this.config.maxLogFileSize}m`,
          maxFiles: this.config.maxLogFiles,
          format: logFormat,
          level: this.config.logLevel
        })
      )

      // Error log file
      transports.push(
        new DailyRotateFile({
          filename: path.join(this.config.logDirectory, 'errors-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: `${this.config.maxLogFileSize}m`,
          maxFiles: this.config.maxLogFiles,
          format: logFormat,
          level: 'error'
        })
      )
    }

    // Initialize main logger
    this.logger = winston.createLogger({
      level: this.config.logLevel,
      format: logFormat,
      transports
    })

    // Error-specific logger
    this.errorLogger = winston.createLogger({
      level: 'error',
      format: logFormat,
      transports: this.config.enableFileLogging ? [
        new DailyRotateFile({
          filename: path.join(this.config.logDirectory, 'error-details-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: `${this.config.maxLogFileSize}m`,
          maxFiles: this.config.maxLogFiles,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      ] : []
    })

    // Performance logger
    this.performanceLogger = winston.createLogger({
      level: 'info',
      format: logFormat,
      transports: this.config.enableFileLogging ? [
        new DailyRotateFile({
          filename: path.join(this.config.logDirectory, 'performance-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: `${this.config.maxLogFileSize}m`,
          maxFiles: this.config.maxLogFiles,
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      ] : []
    })

    console.log(chalk.greenBright('📊 Advanced logging system initialized'))
    console.log(chalk.cyanBright(`Log level: ${this.config.logLevel}`))
    console.log(chalk.blueBright(`File logging: ${this.config.enableFileLogging ? 'enabled' : 'disabled'}`))
  }

  private setupErrorHandlers(): void {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.logCriticalError('Uncaught Exception', error, { component: 'process' })
      process.exit(1)
    })

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.logCriticalError('Unhandled Promise Rejection', reason as Error, {
        component: 'process',
        metadata: { promise: promise.toString() }
      })
    })
  }

  // Basic logging methods
  public debug(message: string, context?: LogContext): void {
    this.logger.debug(message, context)
  }

  public info(message: string, context?: LogContext): void {
    this.logger.info(message, context)
  }

  public warn(message: string, context?: LogContext): void {
    this.logger.warn(message, context)
  }

  public error(message: string, error?: Error, context?: LogContext): void {
    const logData = {
      ...context,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : undefined
    }
    this.logger.error(message, logData)
  }

  // Specialized logging methods
  public logWalletEvent(event: string, walletAddress: string, metadata?: Record<string, any>): void {
    this.info(`Wallet ${event}`, {
      component: 'wallet-tracker',
      walletAddress,
      metadata
    })
  }

  public logTransactionEvent(event: string, transactionSignature: string, walletAddress?: string, metadata?: Record<string, any>): void {
    this.info(`Transaction ${event}`, {
      component: 'transaction-parser',
      transactionSignature,
      walletAddress,
      metadata
    })
  }

  public logApiCall(provider: string, endpoint: string, success: boolean, duration: number, error?: Error): void {
    const message = `API call to ${provider} ${success ? 'succeeded' : 'failed'}`
    const context: LogContext = {
      component: 'api-client',
      duration,
      metadata: { provider, endpoint, success }
    }

    if (success) {
      this.debug(message, context)
    } else {
      this.error(message, error, context)
    }
  }

  public logHeliusCall(endpoint: string, apiKeyIndex: number, success: boolean, duration: number, error?: Error): void {
    if (process.env.LOG_HELIUS_CALLS === 'true') {
      this.logApiCall('Helius', endpoint, success, duration, error)
      
      if (!success && process.env.TRACK_API_ERRORS === 'true') {
        this.trackError({
          message: `Helius API call failed: ${endpoint}`,
          component: 'helius-api',
          context: { metadata: { apiKeyIndex, endpoint } },
          timestamp: new Date(),
          severity: 'medium',
          stack: error?.stack,
          code: 'HELIUS_API_ERROR'
        })
      }
    }
  }

  public logRpcCall(endpoint: string, method: string, success: boolean, duration: number, error?: Error): void {
    if (process.env.LOG_RPC_CALLS === 'true') {
      this.logApiCall('RPC', `${endpoint}/${method}`, success, duration, error)
      
      if (!success && process.env.TRACK_CONNECTION_ERRORS === 'true') {
        this.trackError({
          message: `RPC call failed: ${method}`,
          component: 'rpc-client',
          context: { metadata: { endpoint, method } },
          timestamp: new Date(),
          severity: 'medium',
          stack: error?.stack,
          code: 'RPC_ERROR'
        })
      }
    }
  }

  public logRateLimit(provider: string, apiKeyIndex?: number, resetTime?: Date): void {
    if (process.env.LOG_RATE_LIMITS === 'true') {
      this.warn(`Rate limit hit for ${provider}`, {
        component: 'rate-limiter',
        metadata: { provider, apiKeyIndex, resetTime }
      })
    }
  }

  public logDatabaseQuery(query: string, duration: number, success: boolean, error?: Error): void {
    if (process.env.ENABLE_DB_LOGGING === 'true') {
      const isSlowQuery = duration > parseInt(process.env.SLOW_DB_QUERY_THRESHOLD || '1000')
      
      if (isSlowQuery || !success) {
        const level = !success ? 'error' : 'warn'
        const message = `Database query ${success ? 'slow' : 'failed'}: ${query.substring(0, 100)}...`
        
        this.logger.log(level, message, {
          component: 'database',
          duration,
          metadata: { query: query.substring(0, 200), success, isSlowQuery }
        })
      }
    }
  }

  // Error tracking
  public trackError(errorDetails: ErrorDetails): void {
    if (!this.config.enableErrorTracking) return

    // Log to error logger
    this.errorLogger.error('Error tracked', errorDetails)

    // Console alert for critical errors
    if (this.config.enableConsoleAlerts && errorDetails.severity === 'critical') {
      console.log(chalk.redBright.bold('\n🚨 CRITICAL ERROR DETECTED 🚨'))
      console.log(chalk.redBright(`Component: ${errorDetails.component}`))
      console.log(chalk.redBright(`Message: ${errorDetails.message}`))
      console.log(chalk.redBright(`Time: ${errorDetails.timestamp.toISOString()}`))
      if (errorDetails.stack) {
        console.log(chalk.gray(errorDetails.stack))
      }
      console.log(chalk.redBright('━'.repeat(80)))
    }
  }

  public logCriticalError(message: string, error: Error, context?: LogContext): void {
    this.trackError({
      message,
      stack: error.stack,
      component: context?.component || 'unknown',
      context,
      timestamp: new Date(),
      severity: 'critical',
      code: 'CRITICAL_ERROR'
    })
  }

  // Performance monitoring
  public logPerformance(metrics: PerformanceMetrics): void {
    if (!this.config.enablePerformanceLogging) return

    const isSlowOperation = metrics.duration > this.config.slowOperationThreshold

    if (isSlowOperation || !metrics.success) {
      const level = !metrics.success ? 'error' : 'warn'
      const message = `${metrics.operation} ${metrics.success ? 'slow' : 'failed'} (${metrics.duration}ms)`
      
      this.logger.log(level, message, {
        component: metrics.component,
        duration: metrics.duration,
        metadata: { ...metrics.metadata, isSlowOperation }
      })
    }

    // Log to performance logger
    this.performanceLogger.info('Performance metric', metrics)
  }

  // Statistics and reporting
  public logHourlyStats(stats: Record<string, any>): void {
    if (process.env.ENABLE_STATS_LOGGING === 'true') {
      this.info('Hourly statistics', {
        component: 'statistics',
        metadata: stats
      })
    }
  }

  public logDailyReport(report: Record<string, any>): void {
    if (process.env.ENABLE_DAILY_REPORTS === 'true') {
      this.info('Daily report', {
        component: 'reporting',
        metadata: report
      })
    }
  }

  // Utility methods
  public createTimer(operation: string, component: string): () => void {
    const startTime = Date.now()
    
    return () => {
      const duration = Date.now() - startTime
      this.logPerformance({
        operation,
        duration,
        success: true,
        component
      })
    }
  }

  public async measureAsync<T>(
    operation: string,
    component: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now()
    let success = true
    let error: Error | undefined

    try {
      const result = await fn()
      return result
    } catch (err) {
      success = false
      error = err as Error
      throw err
    } finally {
      const duration = Date.now() - startTime
      this.logPerformance({
        operation,
        duration,
        success,
        component,
        metadata: error ? { error: error.message } : undefined
      })
    }
  }

  public getLoggerConfig(): typeof this.config {
    return { ...this.config }
  }
}

// Export singleton instance
export const logger = LoggerService.getInstance()
