2025-05-29 13:05:46 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":3,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]}}
2025-05-29 13:05:46 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:05:46 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:14:40 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":3,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]}}
2025-05-29 13:14:41 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:14:41 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:19:20 INFO [solana-provider] Solana provider initialized {"metadata":{"rpcEndpointsCount":3,"heliusKeysCount":2,"rpcEndpoints":["https://api.mainnet-beta.solana.com","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]}}
2025-05-29 13:19:21 INFO [monitoring-service] Monitoring service initialized {"metadata":{"statsLogging":true,"dailyReports":true}}
2025-05-29 13:19:21 INFO [main-application] Handi Cat Wallet Tracker starting up {"metadata":{"nodeVersion":"v22.15.0","platform":"win32","environment":"development"}}
2025-05-29 13:19:47 DEBUG [rpc-connection-manager] Selected random RPC connection {"metadata":{"connectionIndex":1,"totalConnections":3}}
