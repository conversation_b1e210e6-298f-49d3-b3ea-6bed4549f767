generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum SubscriptionPlan {
  FREE
  HOBBY
  PRO
  WHALE
}

enum WalletStatus {
  ACTIVE
  USER_PAUSED
  SPAM_PAUSED
  BANNED
}

enum HandiCatStatus {
  ACTIVE
  PAUSED
}

enum PromotionType {
  UPGRADE_TO_50_WALLETS
}

model User {
  id                      String             @id
  username                String
  firstName               String
  lastName                String

  hasDonated              <PERSON>olean          @default(false)

  botStatus               HandiCatStatus   @default(ACTIVE)

  personalWalletPubKey    String
  personalWalletPrivKey   String
  
  userSubscription        UserSubscription?
  userWallets             UserWallet[]
  userPromotions          UserPromotion[]
  groups                  Group[]

  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
}

model Wallet {
  id            String         @id @default(cuid())
  address       String
  
  userWallets   UserWallet[]
}

model UserWallet {
  userId          String
  walletId        String
  name            String
  address         String

  handiCatStatus  HandiCatStatus   @default(ACTIVE)
  status          WalletStatus     @default(ACTIVE)

  user            User             @relation(fields: [userId], references: [id])
  wallet          Wallet           @relation(fields: [walletId], references: [id])

  @@id([userId, walletId]) 
}

model UserSubscription {
  id                             String            @id @default(cuid())
  plan                           SubscriptionPlan  @default(FREE)

  isCanceled                     Boolean           @default(false)
  subscriptionCurrentPeriodEnd   DateTime?

  createdAt                      DateTime          @default(now())
  updatedAt                      DateTime          @updatedAt

  userId                         String            @unique
  user                           User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Promotion {
  id String @id @default(cuid())
  name String
  type PromotionType
  price Decimal
  isActive Boolean @default(true)
  isStackable Boolean 

  userPromotions UserPromotion[]
}

model UserPromotion {
   id String @id @default(cuid())

   purchasedAt DateTime  @default(now())

   userId String 
   user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  promotionId String 
  promotion    Promotion    @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Group {
  id String @id

  name String

  userId String
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}
