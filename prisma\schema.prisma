generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum SubscriptionPlan {
  FREE
  HOBBY
  PRO
  WHALE
}

enum WalletStatus {
  ACTIVE
  USER_PAUSED
  SPAM_PAUSED
  BANNED
}

enum HandiCatStatus {
  ACTIVE
  PAUSED
}

enum PromotionType {
  UPGRADE_TO_50_WALLETS
}

model User {
  id                      String             @id @map("_id")
  username                String
  firstName               String
  lastName                String

  hasDonated              <PERSON>olean          @default(false)

  botStatus               HandiCatStatus   @default(ACTIVE)

  personalWalletPubKey    String
  personalWalletPrivKey   String

  userSubscription        UserSubscription?
  userWallets             UserWallet[]
  userPromotions          UserPromotion[]
  groups                  Group[]

  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
}

model Wallet {
  id            String         @id @map("_id") @default(auto()) @db.ObjectId
  address       String

  userWallets   UserWallet[]
}

model UserWallet {
  id              String           @id @map("_id") @default(auto()) @db.ObjectId
  userId          String
  walletId        String           @db.ObjectId
  name            String
  address         String

  handiCatStatus  HandiCatStatus   @default(ACTIVE)
  status          WalletStatus     @default(ACTIVE)

  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  user            User             @relation(fields: [userId], references: [id])
  wallet          Wallet           @relation(fields: [walletId], references: [id])

  @@unique([userId, walletId])
}

model UserSubscription {
  id                             String            @id @map("_id") @default(auto()) @db.ObjectId
  plan                           SubscriptionPlan  @default(FREE)

  isCanceled                     Boolean           @default(false)
  subscriptionCurrentPeriodEnd   DateTime?

  createdAt                      DateTime          @default(now())
  updatedAt                      DateTime          @updatedAt

  userId                         String            @unique
  user                           User              @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Promotion {
  id String @id @map("_id") @default(auto()) @db.ObjectId
  name String
  type PromotionType
  price Float
  isActive Boolean @default(true)
  isStackable Boolean

  userPromotions UserPromotion[]
}

model UserPromotion {
   id String @id @map("_id") @default(auto()) @db.ObjectId

   purchasedAt DateTime  @default(now())

   userId String
   user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  promotionId String @db.ObjectId
  promotion    Promotion    @relation(fields: [promotionId], references: [id], onDelete: Cascade)
}

model Group {
  id String @id @map("_id")

  name String

  userId String
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
}
