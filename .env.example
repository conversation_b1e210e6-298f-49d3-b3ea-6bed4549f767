# Get this key from https://www.helius.dev For onLogs connection
HELIUS_API_KEY=

# Place in here as many RPC providers you want and separate them with a comma. e.g: https://rpc1.com,https://rpc2.com
# See https://www.quicknode.com or https://chainstack.com for free RPCs. The more connections here, the more wallets you can track
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com

# Get this from Bot Father
BOT_TOKEN=

# Your chat id to access admin commands
ADMIN_CHAT_ID=

# (optional)
# This is the url where your server is deployed, only use this when using webhook Telegram connection (in production)
APP_URL=https://app-url.com/webhook/telegram

HANDICAT_WALLET_ADDRESS=your-wallet-address

NODE_ENV=development

# Database connection string
DATABASE_URL=
