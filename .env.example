# ============================================================================
# HELIUS API KEYS CONFIGURATION (REQUIRED FOR <PERSON><PERSON><PERSON>ITED WALLET TRACKING)
# ============================================================================
# Get multiple API keys from https://www.helius.dev
# Add as many keys as you want, separated by commas (NO SPACES)
#
# EXAMPLE with 5 keys:
# HELIUS_API_KEYS=abc123-def456-ghi789,xyz987-uvw654-rst321,mno147-pqr258-stu369,jkl741-ghi852-def963,vwx159-yza753-bcd486
#
# MINIMUM RECOMMENDED: 3-5 keys for good load balancing
# MORE KEYS = MORE WALLETS YOU CAN TRACK
HELIUS_API_KEYS=

# Legacy single key support (will be used if HELIUS_API_KEYS is empty)
# HELIUS_API_KEY=your-single-key-here

# ============================================================================
# RPC ENDPOINTS CONFIGURATION (IMPORTANT FOR PERFORMANCE)
# ============================================================================
# Add multiple RPC endpoints for load balancing (comma-separated, NO SPACES)
#
# 🆓 FREE RPC OPTIONS (recommended for unlimited wallet tracking):
#
# OPTION 1 - Mix of free providers (RECOMMENDED):
# RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://solana-api.projectserum.com,https://api.metaplex.solana.com
#
# OPTION 2 - QuickNode (free tier: 100k requests/day):
# Get free API key from https://www.quicknode.com/endpoints/solana
# RPC_ENDPOINTS=https://your-quicknode-endpoint.solana-mainnet.quiknode.pro/your-api-key/
#
# OPTION 3 - Chainstack (free tier: 100k requests/day):
# Get free API key from https://chainstack.com/build-better-with-solana/
# RPC_ENDPOINTS=https://solana-mainnet.core.chainstack.com/your-api-key
#
# OPTION 4 - Alchemy (free tier: 300M compute units/month):
# Get free API key from https://www.alchemy.com/solana
# RPC_ENDPOINTS=https://solana-mainnet.g.alchemy.com/v2/your-api-key
#
# 🚀 BEST SETUP (mix multiple free providers):
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://solana-api.projectserum.com

# Get this from Bot Father
BOT_TOKEN=

# Your chat id to access admin commands
ADMIN_CHAT_ID=

# (optional)
# This is the url where your server is deployed, only use this when using webhook Telegram connection (in production)
APP_URL=https://app-url.com/webhook/telegram

HANDICAT_WALLET_ADDRESS=your-wallet-address

NODE_ENV=development

# ============================================================================
# DATABASE CONFIGURATION (CHOOSE ONE)
# ============================================================================
# Set the database provider and connection string
#
# 🗄️ DATABASE OPTIONS:
#
# OPTION 1 - PostgreSQL (original, recommended):
# DATABASE_PROVIDER=postgresql
# DATABASE_URL=postgresql://username:password@localhost:5432/wallet_tracker
#
# OPTION 2 - MySQL (compatible):
# DATABASE_PROVIDER=mysql
# DATABASE_URL=mysql://username:password@localhost:3306/wallet_tracker
#
# OPTION 3 - SQLite (local file, good for testing):
# DATABASE_PROVIDER=sqlite
# DATABASE_URL=file:./wallet_tracker.db
#
# OPTION 4 - MongoDB (requires schema adjustments):
# DATABASE_PROVIDER=mongodb
# DATABASE_URL=**********************************************************
#
# 🆓 FREE DATABASE OPTIONS:
#
# PostgreSQL Free Options:
# - Supabase: https://supabase.com (500MB free)
# - ElephantSQL: https://www.elephantsql.com (20MB free)
# - Aiven: https://aiven.io (1 month free)
#
# MySQL Free Options:
# - PlanetScale: https://planetscale.com (5GB free)
# - Railway: https://railway.app (512MB free)
# - FreeSQLDatabase: https://www.freesqldatabase.com (5MB free)
#
# MongoDB Free Options:
# - MongoDB Atlas: https://www.mongodb.com/atlas (512MB free)
# - Railway: https://railway.app (MongoDB free tier)
#
# 🚀 RECOMMENDED FOR PRODUCTION:
DATABASE_PROVIDER=postgresql
DATABASE_URL=
