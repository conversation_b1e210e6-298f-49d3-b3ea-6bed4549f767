# ============================================================================
# HELIUS API KEYS CONFIGURATION (REQUIRED FOR UNLIMITED WALLET TRACKING)
# ============================================================================
# Get multiple API keys from https://www.helius.dev
# Add as many keys as you want, separated by commas (NO SPACES)
#
# EXAMPLE with 5 keys:
# HELIUS_API_KEYS=abc123-def456-ghi789,xyz987-uvw654-rst321,mno147-pqr258-stu369,jkl741-ghi852-def963,vwx159-yza753-bcd486
#
# MINIMUM RECOMMENDED: 3-5 keys for good load balancing
# MORE KEYS = MORE WALLETS YOU CAN TRACK
HELIUS_API_KEYS=

# Legacy single key support (will be used if HELIUS_API_KEYS is empty)
# HELIUS_API_KEY=your-single-key-here

# Place in here as many RPC providers you want and separate them with a comma. e.g: https://rpc1.com,https://rpc2.com
# See https://www.quicknode.com or https://chainstack.com for free RPCs. The more connections here, the more wallets you can track
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com

# Get this from Bot Father
BOT_TOKEN=

# Your chat id to access admin commands
ADMIN_CHAT_ID=

# (optional)
# This is the url where your server is deployed, only use this when using webhook Telegram connection (in production)
APP_URL=https://app-url.com/webhook/telegram

HANDICAT_WALLET_ADDRESS=your-wallet-address

NODE_ENV=development

# Database connection string
DATABASE_URL=
