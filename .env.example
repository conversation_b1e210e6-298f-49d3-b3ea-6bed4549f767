# ============================================================================
# HELIUS API KEYS CONFIGURATION (REQUIRED FOR <PERSON><PERSON><PERSON>ITED WALLET TRACKING)
# ============================================================================
# Get multiple API keys from https://www.helius.dev
# Add as many keys as you want, separated by commas (NO SPACES)
#
# EXAMPLE with 5 keys:
# HELIUS_API_KEYS=abc123-def456-ghi789,xyz987-uvw654-rst321,mno147-pqr258-stu369,jkl741-ghi852-def963,vwx159-yza753-bcd486
#
# MINIMUM RECOMMENDED: 3-5 keys for good load balancing
# MORE KEYS = MORE WALLETS YOU CAN TRACK
HELIUS_API_KEYS=f3c417b3-e1cf-47ce-99b1-265214ef5770,f63e6d95-a283-4de6-8fa5-e84d419e352e

# Legacy single key support (will be used if HELIUS_API_KEYS is empty)
# HELIUS_API_KEY=your-single-key-here

# ============================================================================
# RPC ENDPOINTS CONFIGURATION (IMPORTANT FOR PERFORMANCE)
# ============================================================================
# Add multiple RPC endpoints for load balancing (comma-separated, NO SPACES)
#
# 🆓 FREE RPC OPTIONS (recommended for unlimited wallet tracking):
#
# OPTION 1 - Mix of free providers (RECOMMENDED):
# RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://solana-api.projectserum.com,https://api.metaplex.solana.com
#
# OPTION 2 - QuickNode (free tier: 100k requests/day):
# Get free API key from https://www.quicknode.com/endpoints/solana
# RPC_ENDPOINTS=https://your-quicknode-endpoint.solana-mainnet.quiknode.pro/your-api-key/
#
# OPTION 3 - Chainstack (free tier: 100k requests/day):
# Get free API key from https://chainstack.com/build-better-with-solana/
# RPC_ENDPOINTS=https://solana-mainnet.core.chainstack.com/your-api-key
#
# OPTION 4 - Alchemy (free tier: 300M compute units/month):
# Get free API key from https://www.alchemy.com/solana
# RPC_ENDPOINTS=https://solana-mainnet.g.alchemy.com/v2/your-api-key
#
# 🚀 BEST SETUP (mix multiple free providers):
RPC_ENDPOINTS=https://api.mainnet-beta.solana.com,https://rpc.ankr.com/solana,https://solana-api.projectserum.com

# ============================================================================
# TELEGRAM BOT CONFIGURATION
# ============================================================================
# Get your bot token from @BotFather on Telegram
BOT_TOKEN=**********************************************

# Your Telegram chat ID (for admin commands)
ADMIN_CHAT_ID=**********

# ============================================================================
# WEBHOOK CONFIGURATION (OPTIONAL - NOT NEEDED FOR WALLET TRACKING)
# ============================================================================
# 🚨 WEBHOOK IS NOT REQUIRED FOR UNLIMITED WALLET TRACKING
#
# The bot uses POLLING mode by default, which is perfect for:
# ✅ Local development and testing
# ✅ Unlimited wallet tracking
# ✅ Simple setup without server configuration
# ✅ No port or SSL certificate requirements
#
# 🌐 ONLY USE WEBHOOK IF:
# • You're deploying to production with a public domain
# • You have SSL certificate setup
# • You need webhook for specific hosting requirements
#
# If you want to use webhook mode (advanced users only):
# 1. Uncomment the webhook section in src/providers/telegram.ts
# 2. Comment out the polling section
# 3. Set APP_URL below to your public domain
# 4. Configure your server to handle webhook requests
#
# APP_URL=https://your-domain.com/webhook/telegram
# WEBHOOK_PORT=8443

# ============================================================================
# PAYMENT CONFIGURATION (OPTIONAL)
# ============================================================================
# This wallet receives payments for subscriptions, donations, and promotions
#
# 💰 IF YOU WANT TO MONETIZE YOUR BOT:
# Set this to your Solana wallet address to receive payments
# HANDICAT_WALLET_ADDRESS=your-solana-wallet-address-here
#
# 🆓 IF YOU WANT FREE-ONLY BOT (NO PAYMENTS):
# Leave empty or set DISABLE_PAYMENTS=true
# HANDICAT_WALLET_ADDRESS=
# DISABLE_PAYMENTS=true
#
# 🚀 RECOMMENDED FOR UNLIMITED TRACKING:
# Since we removed wallet limits, you can leave this empty
HANDICAT_WALLET_ADDRESS=
DISABLE_PAYMENTS=true

NODE_ENV=development

# ============================================================================
# LOGGING & ERROR TRACKING CONFIGURATION
# ============================================================================
# Comprehensive logging for monitoring and debugging
#
# 📊 LOG LEVELS (choose one):
# - error: Only errors
# - warn: Warnings and errors
# - info: General info, warnings, and errors
# - debug: Detailed debugging info (recommended for development)
# - verbose: Everything including API calls
LOG_LEVEL=debug

# 📁 LOG FILE CONFIGURATION:
# Enable file logging (logs saved to files)
ENABLE_FILE_LOGGING=true

# Log directory (relative to project root)
LOG_DIRECTORY=logs

# Maximum log file size before rotation (in MB)
MAX_LOG_FILE_SIZE=50

# Number of log files to keep
MAX_LOG_FILES=10

# 🔍 ERROR TRACKING:
# Enable detailed error tracking and stack traces
ENABLE_ERROR_TRACKING=true

# Track API errors (Helius, RPC failures)
TRACK_API_ERRORS=true

# Track wallet connection errors
TRACK_CONNECTION_ERRORS=true

# Track transaction parsing errors
TRACK_TRANSACTION_ERRORS=true

# 📈 PERFORMANCE MONITORING:
# Enable performance metrics logging
ENABLE_PERFORMANCE_LOGGING=true

# Log slow operations (threshold in milliseconds)
SLOW_OPERATION_THRESHOLD=5000

# 🚨 ALERT CONFIGURATION:
# Enable console alerts for critical errors
ENABLE_CONSOLE_ALERTS=true

# Enable file alerts for critical errors
ENABLE_FILE_ALERTS=true

# 💾 DATABASE LOGGING:
# Enable database query logging
ENABLE_DB_LOGGING=false

# Log slow database queries
LOG_SLOW_DB_QUERIES=true

# Slow query threshold (in milliseconds)
SLOW_DB_QUERY_THRESHOLD=1000

# 🌐 API LOGGING:
# Log all Helius API calls
LOG_HELIUS_CALLS=true

# Log all RPC calls
LOG_RPC_CALLS=false

# Log rate limit events
LOG_RATE_LIMITS=true

# 📊 STATISTICS LOGGING:
# Enable hourly statistics logging
ENABLE_STATS_LOGGING=true

# Enable daily summary reports
ENABLE_DAILY_REPORTS=true

# ============================================================================
# DATABASE CONFIGURATION (CHOOSE ONE)
# ============================================================================
# Set the database provider and connection string
#
# 🗄️ DATABASE OPTIONS:
#
# OPTION 1 - PostgreSQL (original, recommended):
# DATABASE_PROVIDER=postgresql
# DATABASE_URL=postgresql://username:password@localhost:5432/wallet_tracker
#
# OPTION 2 - MySQL (compatible):
# DATABASE_PROVIDER=mysql
# DATABASE_URL=mysql://username:password@localhost:3306/wallet_tracker
#
# OPTION 3 - SQLite (local file, good for testing):
# DATABASE_PROVIDER=sqlite
# DATABASE_URL=file:./wallet_tracker.db
#
# OPTION 4 - MongoDB (requires schema adjustments):
# DATABASE_PROVIDER=mongodb
# DATABASE_URL=**********************************************************
#
# 🆓 FREE DATABASE OPTIONS:
#
# PostgreSQL Free Options:
# - Supabase: https://supabase.com (500MB free)
# - ElephantSQL: https://www.elephantsql.com (20MB free)
# - Aiven: https://aiven.io (1 month free)
#
# MySQL Free Options:
# - PlanetScale: https://planetscale.com (5GB free)
# - Railway: https://railway.app (512MB free)
# - FreeSQLDatabase: https://www.freesqldatabase.com (5MB free)
#
# MongoDB Free Options:
# - MongoDB Atlas: https://www.mongodb.com/atlas (512MB free)
# - Railway: https://railway.app (MongoDB free tier)
#
# 🚀 RECOMMENDED FOR PRODUCTION:
DATABASE_PROVIDER=mongodb
DATABASE_URL=mongodb+srv://damine5254:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
