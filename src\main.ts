import dotenv from 'dotenv'
import { bot } from './providers/telegram'
import { StartCommand } from './bot/commands/start-command'
import { AddCommand } from './bot/commands/add-command'
import { CallbackQueryHandler } from './bot/handlers/callback-query-handler'
import express, { Express } from 'express'
import { DeleteCommand } from './bot/commands/delete-command'
import { TrackWallets } from './lib/track-wallets'
import { CronJobs } from './lib/cron-jobs'
import { RpcConnectionManager } from './providers/solana'
import { PaymentConfigManager } from './config/payment-config'
import { logger } from './services/logger-service'
import { monitoring } from './services/monitoring-service'
import { ASCII_TEXT } from './constants/handi-cat'
import chalk from 'chalk'
import gradient from 'gradient-string'
import { GroupsCommand } from './bot/commands/groups-command'
import { HelpCommand } from './bot/commands/help-command'
import { ManageCommand } from './bot/commands/manage-command'
import { UpgradePlanCommand } from './bot/commands/upgrade-plan-command'
import { AdminCommand } from './bot/commands/admin-command'
import { CleanupCommand } from './bot/commands/cleanup-command'
import { MonitoringCommand } from './bot/commands/monitoring-command'

dotenv.config()

const PORT = process.env.PORT || 3001

class Main {
  private trackWallets: TrackWallets

  private cronJobs: CronJobs
  private callbackQueryHandler: CallbackQueryHandler
  private startCommand: StartCommand
  private addCommand: AddCommand
  private deleteCommand: DeleteCommand
  private groupsCommand: GroupsCommand
  private helpCommand: HelpCommand
  private manageCommand: ManageCommand
  private upgradePlanCommand: UpgradePlanCommand
  private adminCommand: AdminCommand
  private cleanupCommand: CleanupCommand
  private monitoringCommand: MonitoringCommand
  constructor(private app: Express = express()) {
    this.setupMiddleware()
    this.setupRoutes()

    // services
    this.cronJobs = new CronJobs()
    this.trackWallets = new TrackWallets()
    this.callbackQueryHandler = new CallbackQueryHandler(bot)
    this.startCommand = new StartCommand(bot)
    this.addCommand = new AddCommand(bot)
    this.deleteCommand = new DeleteCommand(bot)
    this.groupsCommand = new GroupsCommand(bot)
    this.helpCommand = new HelpCommand(bot)
    this.manageCommand = new ManageCommand(bot)
    this.upgradePlanCommand = new UpgradePlanCommand(bot)
    this.adminCommand = new AdminCommand(bot)
    this.cleanupCommand = new CleanupCommand(bot)
    this.monitoringCommand = new MonitoringCommand(bot)

    this.startServer()
  }

  private setupMiddleware(): void {
    this.app.use(express.json({ limit: '50mb' }))
  }

  private setupRoutes() {
    // Default endpoint
    this.app.get('/', async (req, res) => {
      try {
        res.status(200).send('Hello world')
      } catch (error) {
        console.error('Default route error', error)
        res.status(500).send('Error processing default route')
      }
    })
    this.app.post(`/webhook/telegram`, async (req, res) => {
      try {
        bot.processUpdate(req.body)

        res.status(200).send('Update received')
      } catch (error) {
        console.log('Error processing update:', error)
        res.status(500).send('Error processing update')
      }
    })
  }

  private startServer(): void {
    this.app.listen(PORT, () =>
      console.log(`${chalk.bold.white.bgMagenta(`Server running on http://localhost:${PORT}`)}`),
    )
  }

  public async init(): Promise<void> {
    const gradientText = gradient.retro
    console.log(gradientText(ASCII_TEXT))

    // Log application startup
    logger.info('Handi Cat Wallet Tracker starting up', {
      component: 'main-application',
      metadata: {
        nodeVersion: process.version,
        platform: process.platform,
        environment: process.env.NODE_ENV || 'development'
      }
    })

    // Display enhanced connection statistics
    const stats = RpcConnectionManager.getConnectionStats()
    const paymentConfig = PaymentConfigManager.getConfig()

    console.log(chalk.bold.cyanBright('\n🚀 ENHANCED WALLET TRACKER INITIALIZED'))
    console.log(chalk.greenBright(`📡 RPC Endpoints: ${stats.totalRpcEndpoints}`))
    console.log(chalk.blueBright(`🔑 Helius API Keys: ${stats.availableHeliusKeys}/${stats.totalHeliusKeys} available`))
    console.log(chalk.yellowBright(`💰 Wallet Limits: UNLIMITED (Enhanced for multiple API keys)`))
    console.log(chalk.magentaBright(`⚡ Load Balancing: ENABLED`))
    console.log(chalk.cyanBright(`🛡️  Failover Protection: ENABLED`))

    // Display payment status
    if (paymentConfig.enabled) {
      console.log(chalk.greenBright(`💳 Payment Features: ENABLED`))
      console.log(chalk.cyanBright(`💰 Payment Wallet: ${paymentConfig.walletAddress}`))
    } else {
      console.log(chalk.yellowBright(`🆓 Payment Features: DISABLED (Free mode)`))
      console.log(chalk.blueBright(`💡 All features available without payment`))
    }

    // Display logging status
    const logConfig = logger.getLoggerConfig()
    console.log(chalk.magentaBright(`📊 Logging Level: ${logConfig.logLevel.toUpperCase()}`))
    console.log(chalk.cyanBright(`📁 File Logging: ${logConfig.enableFileLogging ? 'ENABLED' : 'DISABLED'}`))
    console.log(chalk.yellowBright(`🔍 Error Tracking: ${logConfig.enableErrorTracking ? 'ENABLED' : 'DISABLED'}`))
    console.log('')

    // bot
    this.callbackQueryHandler.call()
    this.startCommand.start()
    this.addCommand.addCommandHandler()
    this.deleteCommand.deleteCommandHandler()
    this.groupsCommand.activateGroupCommandHandler()
    this.manageCommand.manageCommandHandler()
    this.upgradePlanCommand.upgradePlanCommandHandler()
    this.helpCommand.groupHelpCommandHandler()
    this.helpCommand.notifyHelpCommandHander()
    this.adminCommand.banWalletCommandHandler()
    this.cleanupCommand.cleanupCommandHandler()
    this.monitoringCommand.monitoringCommandHandler()

    // cron jobs
    await this.cronJobs.monthlySubscriptionFee()
    await this.cronJobs.updateSolPrice()
    await this.cronJobs.sendRenewalReminder()

    // Start automatic wallet cleanup (5-day expiration)
    await this.cronJobs.startWalletCleanup()

    // setup
    await this.trackWallets.setupWalletWatcher({ event: 'initial' })
  }
}

const main = new Main()
main.init()
